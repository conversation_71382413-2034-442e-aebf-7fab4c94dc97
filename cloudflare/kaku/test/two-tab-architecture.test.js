/**
 * Test script for two-tab browser automation architecture
 * This script validates the implementation by testing cross-tab communication,
 * CDP connection persistence, and backward compatibility
 */

import puppeteer from "puppeteer";
import { Hyperbrowser } from "@hyperbrowser/sdk";
import fs from "fs";

const HYPERBROWSER_API_KEY = "hb_28aac10409666bbccf859a9b8804";

(async () => {
  console.log("🚀 Starting two-tab architecture test");

  let browser;
  let wsEndpoint;

  try {
    // Create browser session
    const hyperBrowser = new Hyperbrowser({
      apiKey: HYPERBROWSER_API_KEY,
      timeout: 60000,
    });

    const session = await hyperBrowser.sessions.create({
      browserArgs: ["--auto-accept-this-tab-capture"],
      device: ["desktop"],
    });

    wsEndpoint = session.wsEndpoint;
    browser = await puppeteer.connect({
      browserWSEndpoint: wsEndpoint,
    });

    console.log("✅ Browser session created");

    // Create control tab
    console.log("📋 Creating control tab");
    const controlTab = await browser.newPage();
    await controlTab.goto("about:blank");

    // Create target tab
    console.log("📋 Creating target tab");
    const targetTab = await browser.newPage();
    await targetTab.goto("about:blank");

    console.log("✅ Both tabs created");

    // Load and inject scripts
    console.log("💉 Injecting scripts into both tabs");

    // Inject cross-tab communication utility into both tabs
    const crossTabCommScript = fs.readFileSync("./public/out/cross-tab-communication.6c14934a.min.js", "utf8");
    await controlTab.evaluate(crossTabCommScript);
    await targetTab.evaluate(crossTabCommScript);

    // Inject control tab browser controller
    const controllerControlScript = fs.readFileSync("./public/out/browser-controller-control.c64f0dc8.min.js", "utf8");
    await controlTab.evaluate(controllerControlScript);

    // Inject proxy browser controller into target tab
    const controllerProxyScript = fs.readFileSync("./public/out/browser-controller-proxy.38c8a21b.min.js", "utf8");
    await targetTab.evaluate(controllerProxyScript);

    console.log("✅ Scripts injected");

    // Initialize control tab
    console.log("🔧 Initializing control tab");
    const controlTargetId = controlTab.target()._targetId || controlTab.target()._targetInfo?.targetId;
    
    await controlTab.evaluate(
      (ws, targetId) => {
        return window.browserControllerControl.init(ws, targetId);
      },
      wsEndpoint,
      controlTargetId
    );

    console.log("✅ Control tab initialized");

    // Initialize proxy in target tab
    console.log("🔧 Initializing target tab proxy");
    await targetTab.evaluate(() => {
      return window.browserController.init();
    });

    console.log("✅ Target tab proxy initialized");

    // Test 1: Basic communication
    console.log("🧪 Test 1: Basic cross-tab communication");
    const pingResult = await targetTab.evaluate(() => {
      return window.browserController.ping();
    });

    console.log("✅ Ping result:", pingResult);

    // Test 2: Screenshot functionality
    console.log("🧪 Test 2: Screenshot functionality");
    const screenshotResult = await targetTab.evaluate(() => {
      return window.browserController.takeScreenshot();
    });

    console.log("✅ Screenshot result:", {
      success: screenshotResult.success,
      dataLength: screenshotResult.data ? screenshotResult.data.length : 0,
      timestamp: screenshotResult.timestamp
    });

    // Test 3: Navigation persistence
    console.log("🧪 Test 3: Navigation persistence test");
    
    // Navigate target tab to a real page
    await targetTab.goto("https://example.com");
    console.log("→ Target tab navigated to example.com");

    // Wait a moment for navigation to complete
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test if communication still works after navigation
    const postNavigationPing = await targetTab.evaluate(() => {
      return window.browserController.ping();
    });

    console.log("✅ Post-navigation ping result:", postNavigationPing);

    // Test 4: Mouse interaction
    console.log("🧪 Test 4: Mouse interaction test");
    const mouseResult = await targetTab.evaluate(() => {
      return window.browserController.dispatchMouseMove(100, 100);
    });

    console.log("✅ Mouse move result: success");

    // Test 5: Page info retrieval
    console.log("🧪 Test 5: Page info retrieval");
    const pageInfo = await targetTab.evaluate(() => {
      return window.browserController.getPageInfo();
    });

    console.log("✅ Page info:", pageInfo);

    console.log("🎉 All tests passed! Two-tab architecture is working correctly.");

  } catch (error) {
    console.error("❌ Test failed:", error);
    process.exit(1);
  } finally {
    if (browser) {
      await browser.close();
      console.log("🧹 Browser closed");
    }
  }
})();
