import { CrossTabCommunicator } from './utils/cross-tab-communication';

declare global {
  interface Window {
    browserController: any;
  }
}

/**
 * Target tab browser controller proxy - lightweight interface for user interaction
 * Runs in the main user interaction tab and proxies all CDP operations to control tab
 * Maintains backward compatibility with existing window.browserController interface
 */
(function () {
  const config = {
    debug: true,
  };

  let communicator: CrossTabCommunicator | null = null;
  let isInitialized = false;

  function log(...args: any[]) {
    if (config.debug) {
      console.log('[browserControllerProxy]', ...args);
    }
  }

  function error(...args: any[]) {
    console.error('[browserControllerProxy]', ...args);
  }

  /**
   * Initialize the proxy browser controller
   * This is a lightweight initialization that sets up cross-tab communication
   */
  async function init(): Promise<void> {
    log('Initializing proxy browser controller');

    if (isInitialized) {
      log('Proxy already initialized');
      return;
    }

    // Initialize cross-tab communication
    log('Setting up cross-tab communication...');
    communicator = new CrossTabCommunicator({
      channelName: 'browser-controller',
      debug: config.debug,
      timeout: 15000,
    });

    // Wait a moment for the control tab to be ready
    log('Waiting for control tab to be ready...');
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Test communication with control tab with retries
    let attempts = 0;
    const maxAttempts = 10;

    while (attempts < maxAttempts) {
      try {
        log(`Testing connection to control tab (attempt ${attempts + 1}/${maxAttempts})...`);
        const pingResult = await communicator.sendMessage('ping');
        log('Successfully connected to control tab:', pingResult);
        isInitialized = true;
        break;
      } catch (err) {
        attempts++;
        if (attempts >= maxAttempts) {
          error('Failed to connect to control tab after all attempts:', err);
          throw new Error('Failed to establish communication with control tab');
        }
        log(`Connection attempt ${attempts} failed, retrying in 500ms...`);
        await new Promise((resolve) => setTimeout(resolve, 500));
      }
    }

    log('Proxy browser controller initialized successfully');
  }

  /**
   * Ensure the proxy is initialized before making calls
   */
  function ensureInitialized(): void {
    if (!isInitialized || !communicator) {
      throw new Error('Browser controller proxy not initialized. Call init() first.');
    }
  }

  /**
   * Setup browser metrics (viewport, device scale, etc.)
   */
  async function setupBrowserMetrics(viewport: { width: number; height: number }): Promise<void> {
    ensureInitialized();
    log('Setting up browser metrics:', viewport);

    const result = await communicator!.sendMessage('setupBrowserMetrics', { viewport });
    if (!result.success) {
      throw new Error(result.error || 'Failed to setup browser metrics');
    }
  }

  /**
   * Dispatch mouse move event
   */
  async function dispatchMouseMove(x: number, y: number): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse move to (${x}, ${y})`);

    const result = await communicator!.sendMessage('dispatchMouseMove', { x, y });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse move');
    }
  }

  /**
   * Dispatch mouse down event
   */
  async function dispatchMouseDown(
    x: number,
    y: number,
    button: 'left' | 'right' | 'middle' = 'left',
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse down at (${x}, ${y}) with button: ${button}`);

    const result = await communicator!.sendMessage('dispatchMouseDown', { x, y, button });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse down');
    }
  }

  /**
   * Dispatch mouse up event
   */
  async function dispatchMouseUp(
    x: number,
    y: number,
    button: 'left' | 'right' | 'middle' = 'left',
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse up at (${x}, ${y}) with button: ${button}`);

    const result = await communicator!.sendMessage('dispatchMouseUp', { x, y, button });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse up');
    }
  }

  /**
   * Dispatch mouse click event
   */
  async function dispatchMouseClick(
    x: number,
    y: number,
    button: 'left' | 'right' | 'middle' = 'left',
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching mouse click at (${x}, ${y}) with button: ${button}`);

    const result = await communicator!.sendMessage('dispatchMouseClick', { x, y, button });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch mouse click');
    }
  }

  /**
   * Dispatch key event
   */
  async function dispatchKeyEvent(
    type: 'keyDown' | 'keyUp' | 'char',
    key: string,
    code?: string,
  ): Promise<void> {
    ensureInitialized();
    log(`Dispatching key event: ${type} ${key}`);

    const result = await communicator!.sendMessage('dispatchKeyEvent', { type, key, code });
    if (!result.success) {
      throw new Error(result.error || 'Failed to dispatch key event');
    }
  }

  /**
   * Insert text
   */
  async function insertText(text: string): Promise<void> {
    ensureInitialized();
    log(`Inserting text: ${text}`);

    const result = await communicator!.sendMessage('insertText', { text });
    if (!result.success) {
      throw new Error(result.error || 'Failed to insert text');
    }
  }

  /**
   * Take a screenshot
   */
  async function takeScreenshot(): Promise<{
    success: boolean;
    data?: string;
    timestamp?: number;
  }> {
    ensureInitialized();
    log('Taking screenshot');

    const result = await communicator!.sendMessage('takeScreenshot');
    if (!result.success) {
      throw new Error(result.error || 'Failed to take screenshot');
    }

    return result;
  }

  /**
   * Handle input event (for backward compatibility)
   */
  async function handleInputEvent(event: any): Promise<void> {
    ensureInitialized();
    log('Handling input event:', event);

    // This is a placeholder for input event handling
    // The actual implementation would depend on the specific event type
    switch (event.type) {
      case 'click':
        await dispatchMouseClick(event.x, event.y);
        break;
      case 'keydown':
        await dispatchKeyEvent('keyDown', event.key, event.code);
        break;
      case 'keyup':
        await dispatchKeyEvent('keyUp', event.key, event.code);
        break;
      default:
        log('Unhandled input event type:', event.type);
    }
  }

  /**
   * Request new frame generation
   */
  async function requestNewFrame(): Promise<void> {
    ensureInitialized();
    log('Requesting new frame');

    const result = await communicator!.sendMessage('requestNewFrame');
    if (!result.success) {
      throw new Error(result.error || 'Failed to request new frame');
    }
  }

  /**
   * Trigger mouse movement for frame generation
   */
  async function triggerMouseMovement(): Promise<void> {
    ensureInitialized();
    log('Triggering mouse movement');

    const result = await communicator!.sendMessage('triggerMouseMovement');
    if (!result.success) {
      throw new Error(result.error || 'Failed to trigger mouse movement');
    }
  }

  /**
   * Get page information
   */
  async function getPageInfo(): Promise<any> {
    ensureInitialized();
    log('Getting page info');

    const result = await communicator!.sendMessage('getPageInfo');
    if (!result.success) {
      throw new Error(result.error || 'Failed to get page info');
    }

    return result.data;
  }

  /**
   * Ping the control tab (for testing connectivity)
   */
  async function ping(): Promise<any> {
    ensureInitialized();
    log('Pinging control tab');

    const result = await communicator!.sendMessage('ping');
    return result;
  }

  // Expose public API with backward compatibility
  (globalThis as any).browserController = {
    init,
    setupBrowserMetrics,
    dispatchMouseMove,
    dispatchMouseDown,
    dispatchMouseUp,
    dispatchMouseClick,
    dispatchKeyEvent,
    insertText,
    takeScreenshot,
    handleInputEvent,
    requestNewFrame,
    triggerMouseMovement,
    getPageInfo,
    ping,
  };

  log('Proxy browser controller script loaded');
})();
