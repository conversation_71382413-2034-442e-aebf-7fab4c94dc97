/**
 * Cross-tab communication utilities for two-tab browser automation architecture
 * Supports BroadcastChannel API with fallback to DOM events for isolated world compatibility
 */

export interface CrossTabMessage {
  id: string;
  type: string;
  data?: any;
  timestamp: number;
}

export interface CrossTabResponse {
  id: string;
  type: 'response';
  result: any;
  timestamp: number;
}

export interface CrossTabCommunicationOptions {
  channelName: string;
  timeout?: number;
  debug?: boolean;
  fallbackToDOMEvents?: boolean;
}

export class CrossTabCommunicator {
  private channel: BroadcastChannel | null = null;
  private messageId = 0;
  private pendingMessages = new Map<string, { resolve: Function; reject: Function }>();
  private options: Required<CrossTabCommunicationOptions>;

  constructor(options: CrossTabCommunicationOptions) {
    this.options = {
      timeout: 10000,
      debug: false,
      fallbackToDOMEvents: true,
      ...options,
    };

    this.initializeCommunication();
  }

  private log(...args: any[]) {
    if (this.options.debug) {
      console.log('[CrossTabCommunicator]', ...args);
    }
  }

  private error(...args: any[]) {
    console.error('[CrossTabCommunicator]', ...args);
  }

  private async initializeCommunication(): Promise<void> {
    try {
      // Try BroadcastChannel first
      this.channel = new BroadcastChannel(this.options.channelName);
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this));
      this.log('BroadcastChannel initialized successfully');
    } catch (err) {
      this.error('BroadcastChannel initialization failed:', err);
    }
  }

  private handleBroadcastMessage(event: MessageEvent): void {
    this.handleMessage(event.data);
  }

  private handleDOMMessage(event: CustomEvent): void {
    this.handleMessage(event.detail);
  }

  private handleMessage(data: CrossTabMessage | CrossTabResponse): void {
    this.log('Received message:', data);

    if (data.type === 'response' && this.pendingMessages.has(data.id)) {
      const { resolve, reject } = this.pendingMessages.get(data.id)!;
      this.pendingMessages.delete(data.id);

      const response = data as CrossTabResponse;
      if (response.result?.success !== false) {
        resolve(response.result);
      } else {
        reject(new Error(response.result?.error || 'Cross-tab operation failed'));
      }
    }
  }

  /**
   * Send a message to other tabs and wait for response
   */
  async sendMessage(type: string, data: any = {}): Promise<any> {
    return new Promise((resolve, reject) => {
      const id = `msg_${++this.messageId}_${Date.now()}`;
      const message: CrossTabMessage = {
        id,
        type,
        data,
        timestamp: Date.now(),
      };

      // Store pending message
      this.pendingMessages.set(id, { resolve, reject });

      // Set timeout
      setTimeout(() => {
        if (this.pendingMessages.has(id)) {
          this.pendingMessages.delete(id);
          reject(new Error(`Cross-tab communication timeout for message type: ${type}`));
        }
      }, this.options.timeout);

      // Send message
      this.sendMessageInternal(message);
    });
  }

  /**
   * Send a response to a received message
   */
  sendResponse(originalMessage: CrossTabMessage, result: any): void {
    const response: CrossTabResponse = {
      id: originalMessage.id,
      type: 'response',
      result,
      timestamp: Date.now(),
    };

    this.sendMessageInternal(response);
  }

  private sendMessageInternal(message: CrossTabMessage | CrossTabResponse): void {
    this.log('Sending message:', message);

    // Use BroadcastChannel
    this.channel.postMessage(message);
  }

  /**
   * Add a message handler for incoming messages
   */
  onMessage(handler: (message: CrossTabMessage) => Promise<any> | any): void {
    const messageHandler = async (data: CrossTabMessage | CrossTabResponse) => {
      if (data.type !== 'response') {
        const message = data as CrossTabMessage;
        try {
          const result = await handler(message);
          this.sendResponse(message, result);
        } catch (error) {
          this.error('Message handler error:', error);
          this.sendResponse(message, {
            success: false,
            error: error instanceof Error ? error.message : 'Unknown error',
          });
        }
      }
    };

    this.channel.addEventListener('message', (event: MessageEvent) => messageHandler(event.data));
  }

  /**
   * Close the communication channel
   */
  close(): void {
    if (this.channel) {
      this.channel.close();
      this.channel = null;
    }

    // Reject all pending messages
    for (const [id, { reject }] of this.pendingMessages) {
      reject(new Error('Communication channel closed'));
    }
    this.pendingMessages.clear();

    this.log('Communication channel closed');
  }
}
